using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyApp;

namespace UTLab04
{
    [TestClass]
    public class UTLab04
    {
        private MethodLibrary.MethodLibrary obj = new MethodLibrary.MethodLibrary();

        [TestMethod]
        public void Test1()
        {
            long k = obj.Sum(8, out long s);

            Assert.AreEqual(10, s, "Gi<PERSON> trị s không đúng");
            Assert.AreEqual(4, k, "Giá trị k không đúng");
        }

        [TestMethod]
        public void Test2()
        {
            long k = obj.Sum(-9, out long s);

            Assert.AreEqual(1, s, "<PERSON><PERSON><PERSON> trị s không đúng");
            Assert.AreEqual(1, k, "Giá trị k không đúng");
        }

        [TestMethod]
        public void Test3()
        {
            long k = obj.Sum(0, out long s);

            Assert.AreEqual(1, s, "<PERSON><PERSON><PERSON> trị s không đúng");
            Assert.AreEqual(1, k, "<PERSON><PERSON><PERSON> trị k không đúng");
        }
    }
}
